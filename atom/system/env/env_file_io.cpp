/*
 * env_file_io.cpp
 *
 * Copyright (C) 2023-2024 Max <PERSON> <lightapt.com>
 */

/*************************************************

Date: 2023-12-16

Description: Environment variable file I/O operations implementation

**************************************************/

#include "env_file_io.hpp"

#include <fstream>

#include "env_core.hpp"
#include <spdlog/spdlog.h>

namespace atom::utils {

auto EnvFileIO::saveToFile(const std::filesystem::path& filePath,
                           const HashMap<String, String>& vars,
                           EnvFileFormat format) -> bool {
    try {
        // Auto-detect format if needed
        if (format == EnvFileFormat::AUTO) {
            format = detectFileFormat(filePath);
        }

        HashMap<String, String> varsToSave;
        if (vars.empty()) {
            varsToSave = EnvCore::Environ();
        } else {
            varsToSave = vars;
        }

        // Use format-specific writers for non-dotenv formats
        switch (format) {
            case EnvFileFormat::JSON:
                return writeJsonFile(filePath, varsToSave);
            case EnvFileFormat::YAML:
                return writeYamlFile(filePath, varsToSave);
            case EnvFileFormat::XML:
                return writeXmlFile(filePath, varsToSave);
            case EnvFileFormat::INI:
                return writeIniFile(filePath, varsToSave);
            case EnvFileFormat::SHELL:
                return writeShellFile(filePath, varsToSave);
            default:
                break;
        }

        // Default dotenv format
        std::ofstream file(filePath);
        if (!file.is_open()) {
            spdlog::error("Failed to open file for writing: {}",
                          filePath.string());
            return false;
        }

        file << "# Environment variables file\n";
        file << "# Generated by Atom Environment Manager\n";
        file << "# Format: KEY=VALUE\n\n";

        for (const auto& [key, value] : varsToSave) {
            if (isValidKey(key)) {
                file << formatLine(key, value, format) << '\n';
            } else {
                spdlog::warn("Skipping invalid key: {}", key);
            }
        }

        file.close();
        spdlog::info("Successfully saved {} variables to {}",
                     varsToSave.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Exception while saving to file: {}", e.what());
        return false;
    }
}

auto EnvFileIO::loadFromFile(const std::filesystem::path& filePath,
                             bool overwrite, EnvFileFormat format) -> bool {
    try {
        std::ifstream file(filePath);
        if (!file.is_open()) {
            spdlog::error("Failed to open file for reading: {}",
                          filePath.string());
            return false;
        }

        HashMap<String, String> loadedVars;
        String line;
        int lineNumber = 0;

        while (std::getline(file, line)) {
            lineNumber++;

            // Skip empty lines and comments
            if (line.empty() || line[0] == '#') {
                continue;
            }

            auto [key, value] = parseLine(line, format);
            if (!key.empty()) {
                if (!isValidKey(key)) {
                    spdlog::warn("Invalid key at line {}: {}", lineNumber, key);
                    continue;
                }

                if (!overwrite && !EnvCore::getEnv(key, "").empty()) {
                    spdlog::debug("Skipping existing variable: {}", key);
                    continue;
                }

                loadedVars[key] = value;
            } else {
                spdlog::warn("Failed to parse line {}: {}", lineNumber, line);
            }
        }

        file.close();

        // Set the environment variables
        for (const auto& [key, value] : loadedVars) {
            EnvCore::setEnv(key, value);
        }

        spdlog::info("Successfully loaded {} variables from {}",
                     loadedVars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Exception while loading from file: {}", e.what());
        return false;
    }
}

auto EnvFileIO::parseLine(const String& line, EnvFileFormat format) -> std::pair<String, String> {
    size_t eq_pos = line.find('=');
    if (eq_pos == String::npos || eq_pos == 0) {
        return {"", ""};
    }

    String key = line.substr(0, eq_pos);
    String value = line.substr(eq_pos + 1);

    // Trim whitespace from key
    size_t start = key.find_first_not_of(" \t");
    size_t end = key.find_last_not_of(" \t");
    if (start != String::npos && end != String::npos) {
        key = key.substr(start, end - start + 1);
    } else {
        return {"", ""};
    }

    // Unescape value
    value = unescapeValue(value, format);

    return {key, value};
}

auto EnvFileIO::formatLine(const String& key, const String& value, EnvFileFormat format) -> String {
    switch (format) {
        case EnvFileFormat::SHELL:
            return "export " + key + "=" + escapeValue(value, format);
        default:
            return key + "=" + escapeValue(value, format);
    }
}

auto EnvFileIO::isValidKey(const String& key) -> bool {
    if (key.empty()) {
        return false;
    }

    // Check if key starts with a letter or underscore
    if (!std::isalpha(key[0]) && key[0] != '_') {
        return false;
    }

    // Check if key contains only alphanumeric characters and underscores
    for (char c : key) {
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }

    return true;
}

auto EnvFileIO::escapeValue(const String& value, EnvFileFormat format) -> String {
    (void)format;  // Suppress unused parameter warning for now
    String escaped;
    escaped.reserve(value.length());

    for (char c : value) {
        switch (c) {
            case '\n':
                escaped += "\\n";
                break;
            case '\r':
                escaped += "\\r";
                break;
            case '\t':
                escaped += "\\t";
                break;
            case '\\':
                escaped += "\\\\";
                break;
            case '"':
                escaped += "\\\"";
                break;
            default:
                escaped += c;
                break;
        }
    }

    // Quote the value if it contains spaces
    if (value.find(' ') != String::npos || value.find('\t') != String::npos) {
        return "\"" + escaped + "\"";
    }

    return escaped;
}

auto EnvFileIO::unescapeValue(const String& value, EnvFileFormat format) -> String {
    (void)format;  // Suppress unused parameter warning for now
    String unescaped;
    unescaped.reserve(value.length());

    String input = value;

    // Remove quotes if present
    if (input.length() >= 2 && input.front() == '"' && input.back() == '"') {
        input = input.substr(1, input.length() - 2);
    }

    for (size_t i = 0; i < input.length(); ++i) {
        if (input[i] == '\\' && i + 1 < input.length()) {
            switch (input[i + 1]) {
                case 'n':
                    unescaped += '\n';
                    ++i;
                    break;
                case 'r':
                    unescaped += '\r';
                    ++i;
                    break;
                case 't':
                    unescaped += '\t';
                    ++i;
                    break;
                case '\\':
                    unescaped += '\\';
                    ++i;
                    break;
                case '"':
                    unescaped += '"';
                    ++i;
                    break;
                default:
                    unescaped += input[i];
                    break;
            }
        } else {
            unescaped += input[i];
        }
    }

    return unescaped;
}

// ========== NEW ENHANCED FUNCTIONALITY ==========

auto EnvFileIO::saveToFileAtomic(const std::filesystem::path& filePath,
                                 const HashMap<String, String>& vars,
                                 EnvFileFormat format) -> bool {
    try {
        // Create temporary file
        auto tempPath = filePath;
        tempPath += ".tmp";

        // Save to temporary file first
        if (!saveToFile(tempPath, vars, format)) {
            return false;
        }

        // Create backup if original exists
        if (std::filesystem::exists(filePath)) {
            createBackupFile(filePath);
        }

        // Atomically move temp file to final location
        std::filesystem::rename(tempPath, filePath);

        spdlog::info("Atomically saved {} variables to {}", vars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to atomically save file '{}': {}", filePath.string(), e.what());
        return false;
    }
}

auto EnvFileIO::validateFile(const std::filesystem::path& filePath,
                             EnvFileFormat format) -> bool {
    try {
        if (!std::filesystem::exists(filePath)) {
            spdlog::error("File does not exist: {}", filePath.string());
            return false;
        }

        if (format == EnvFileFormat::AUTO) {
            format = detectFileFormat(filePath);
        }

        std::ifstream file(filePath);
        if (!file.is_open()) {
            spdlog::error("Cannot open file for validation: {}", filePath.string());
            return false;
        }

        String line;
        int lineNumber = 0;

        while (std::getline(file, line)) {
            lineNumber++;

            if (isCommentLine(line) || line.empty()) {
                continue;
            }

            auto [key, value] = parseLine(line, format);
            if (key.empty()) {
                spdlog::error("Invalid line {} in file {}: {}", lineNumber, filePath.string(), line);
                return false;
            }

            if (!isValidKey(key)) {
                spdlog::error("Invalid key '{}' at line {} in file {}", key, lineNumber, filePath.string());
                return false;
            }
        }

        spdlog::debug("File validation successful: {}", filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Exception during file validation: {}", e.what());
        return false;
    }
}

auto EnvFileIO::detectFileFormat(const std::filesystem::path& filePath) -> EnvFileFormat {
    String extension = getFileExtension(filePath);

    if (extension == ".json") return EnvFileFormat::JSON;
    if (extension == ".yaml" || extension == ".yml") return EnvFileFormat::YAML;
    if (extension == ".xml") return EnvFileFormat::XML;
    if (extension == ".ini") return EnvFileFormat::INI;
    if (extension == ".sh" || extension == ".bash") return EnvFileFormat::SHELL;

    // Try to detect from content
    try {
        std::ifstream file(filePath);
        if (file.is_open()) {
            String firstLine;
            std::getline(file, firstLine);

            if (firstLine.find("export ") != String::npos) {
                return EnvFileFormat::SHELL;
            }
            if (firstLine.find("{") != String::npos) {
                return EnvFileFormat::JSON;
            }
            if (firstLine.find("[") != String::npos) {
                return EnvFileFormat::INI;
            }
        }
    } catch (...) {
        // Ignore errors, fall back to default
    }

    return EnvFileFormat::DOTENV;  // Default
}

auto EnvFileIO::mergeFiles(const std::vector<std::filesystem::path>& filePaths,
                           const std::filesystem::path& outputPath,
                           EnvFileFormat format) -> bool {
    try {
        HashMap<String, String> mergedVars;

        for (const auto& filePath : filePaths) {
            if (!std::filesystem::exists(filePath)) {
                spdlog::warn("Skipping non-existent file: {}", filePath.string());
                continue;
            }

            // Load variables from each file
            auto currentEnv = EnvCore::Environ();
            if (loadFromFile(filePath, true, detectFileFormat(filePath))) {
                auto newEnv = EnvCore::Environ();

                // Find new variables added by this file
                for (const auto& [key, value] : newEnv) {
                    if (currentEnv.find(key) == currentEnv.end() || currentEnv[key] != value) {
                        mergedVars[key] = value;
                    }
                }
            }
        }

        return saveToFile(outputPath, mergedVars, format);
    } catch (const std::exception& e) {
        spdlog::error("Failed to merge files: {}", e.what());
        return false;
    }
}

// Utility method implementations
auto EnvFileIO::createBackupFile(const std::filesystem::path& filePath) -> bool {
    try {
        auto backupPath = filePath;
        backupPath += ".backup";
        std::filesystem::copy_file(filePath, backupPath,
                                   std::filesystem::copy_options::overwrite_existing);
        spdlog::debug("Created backup: {}", backupPath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to create backup for '{}': {}", filePath.string(), e.what());
        return false;
    }
}

auto EnvFileIO::trimString(const String& str) -> String {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == String::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

auto EnvFileIO::isCommentLine(const String& line) -> bool {
    String trimmed = trimString(line);
    return trimmed.empty() || trimmed[0] == '#' || trimmed[0] == ';';
}

auto EnvFileIO::getFileExtension(const std::filesystem::path& filePath) -> String {
    String ext = filePath.extension().string();
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    return ext;
}

// Format-specific writer implementations (basic versions)
auto EnvFileIO::writeJsonFile(const std::filesystem::path& filePath,
                              const HashMap<String, String>& vars) -> bool {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) return false;

        file << "{\n";
        bool first = true;
        for (const auto& [key, value] : vars) {
            if (!first) file << ",\n";
            file << "  \"" << key << "\": \"" << value << "\"";
            first = false;
        }
        file << "\n}\n";

        spdlog::info("Saved {} variables to JSON file: {}", vars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to write JSON file: {}", e.what());
        return false;
    }
}

auto EnvFileIO::writeYamlFile(const std::filesystem::path& filePath,
                              const HashMap<String, String>& vars) -> bool {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) return false;

        file << "# Environment variables\n";
        for (const auto& [key, value] : vars) {
            file << key << ": \"" << value << "\"\n";
        }

        spdlog::info("Saved {} variables to YAML file: {}", vars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to write YAML file: {}", e.what());
        return false;
    }
}

auto EnvFileIO::writeXmlFile(const std::filesystem::path& filePath,
                             const HashMap<String, String>& vars) -> bool {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) return false;

        file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        file << "<environment>\n";
        for (const auto& [key, value] : vars) {
            file << "  <variable name=\"" << key << "\" value=\"" << value << "\"/>\n";
        }
        file << "</environment>\n";

        spdlog::info("Saved {} variables to XML file: {}", vars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to write XML file: {}", e.what());
        return false;
    }
}

auto EnvFileIO::writeIniFile(const std::filesystem::path& filePath,
                             const HashMap<String, String>& vars) -> bool {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) return false;

        file << "[environment]\n";
        for (const auto& [key, value] : vars) {
            file << key << "=" << value << "\n";
        }

        spdlog::info("Saved {} variables to INI file: {}", vars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to write INI file: {}", e.what());
        return false;
    }
}

auto EnvFileIO::writeShellFile(const std::filesystem::path& filePath,
                               const HashMap<String, String>& vars) -> bool {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) return false;

        file << "#!/bin/bash\n";
        file << "# Environment variables\n\n";
        for (const auto& [key, value] : vars) {
            file << "export " << key << "=\"" << value << "\"\n";
        }

        spdlog::info("Saved {} variables to shell file: {}", vars.size(), filePath.string());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to write shell file: {}", e.what());
        return false;
    }
}

// Format-specific parser stubs (basic implementations)
auto EnvFileIO::parseJsonFile(const std::filesystem::path& filePath) -> HashMap<String, String> {
    // Basic JSON parsing - in a real implementation, use a proper JSON library
    HashMap<String, String> result;
    spdlog::warn("JSON parsing not fully implemented yet for file: {}", filePath.string());
    return result;
}

auto EnvFileIO::parseYamlFile(const std::filesystem::path& filePath) -> HashMap<String, String> {
    // Basic YAML parsing - in a real implementation, use a proper YAML library
    HashMap<String, String> result;
    spdlog::warn("YAML parsing not fully implemented yet for file: {}", filePath.string());
    return result;
}

auto EnvFileIO::parseXmlFile(const std::filesystem::path& filePath) -> HashMap<String, String> {
    // Basic XML parsing - in a real implementation, use a proper XML library
    HashMap<String, String> result;
    spdlog::warn("XML parsing not fully implemented yet for file: {}", filePath.string());
    return result;
}

auto EnvFileIO::parseIniFile(const std::filesystem::path& filePath) -> HashMap<String, String> {
    // Basic INI parsing - in a real implementation, use a proper INI library
    HashMap<String, String> result;
    spdlog::warn("INI parsing not fully implemented yet for file: {}", filePath.string());
    return result;
}

auto EnvFileIO::parseShellFile(const std::filesystem::path& filePath) -> HashMap<String, String> {
    // Basic shell script parsing - in a real implementation, use a proper parser
    HashMap<String, String> result;
    spdlog::warn("Shell script parsing not fully implemented yet for file: {}", filePath.string());
    return result;
}

}  // namespace atom::utils
